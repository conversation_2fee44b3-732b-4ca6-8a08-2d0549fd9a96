import cv2
import time
from .logger import get_stream_logger

class RTSPStreamer:
    def __init__(self, rtsp_url, reconnect_wait_time=2, error_wait_time=0.5):
        self.rtsp_url = rtsp_url
        self.reconnect_wait_time = reconnect_wait_time
        self.error_wait_time = error_wait_time
        self.cap = None
        self.logger = get_stream_logger("RTSPStreamer")

    def connect(self):
        self.logger.info(f"连接到 RTSP: {self.rtsp_url}")
        self.cap = cv2.VideoCapture(self.rtsp_url)
        if not self.cap.isOpened():
            self.logger.error("连接失败")
            return False
        return True

    def read_frame(self):
        if self.cap is None or not self.cap.isOpened():
            self.logger.warning("视频流断开，重新连接中...")
            time.sleep(self.reconnect_wait_time)
            self.connect()

        ret, frame = self.cap.read()
        if not ret:
            self.logger.warning("帧读取失败")
            return None
        return frame

    def release(self):
        if self.cap:
            self.cap.release()
            self.logger.info("RTSP连接已释放")
