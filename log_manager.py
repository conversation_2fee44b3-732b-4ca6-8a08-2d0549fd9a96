import logging
import os
from datetime import datetime, timedelta

def setup_logger(config: dict):
    logger = logging.getLogger("WetSlagLogger")
    logger.setLevel(logging.INFO)

    if logger.handlers:
        return logger

    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    if config.get("save_logs", True):
        log_dir = config.get("output_dir", "logs")
        os.makedirs(log_dir, exist_ok=True)
        date_str = datetime.now().strftime("%Y%m%d")
        log_file = os.path.join(log_dir, f"conveyor_detection_{date_str}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        if config.get("clean_logs", True):
            clean_old_logs(log_dir, config.get("clean_day", 7), logger)

    return logger

def clean_old_logs(log_dir, keep_days, logger=None):
    now = datetime.now()
    for fname in os.listdir(log_dir):
        fpath = os.path.join(log_dir, fname)
        if os.path.isfile(fpath):
            ftime = datetime.fromtimestamp(os.path.getmtime(fpath))
            if (now - ftime).days > keep_days:
                try:
                    os.remove(fpath)
                    if logger:
                        logger.info(f"已清理旧日志: {fname}")
                except Exception as e:
                    if logger:
                        logger.error(f"清理日志失败: {fname}, 原因: {e}")