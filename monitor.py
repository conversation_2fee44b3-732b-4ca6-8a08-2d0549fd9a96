import torch
import psutil
import logging

def log_system_state(logger: logging.Logger):
    process = psutil.Process()
    mem_mb = process.memory_info().rss / 1024 / 1024
    logger.debug(f"内存使用: {mem_mb:.2f} MB")

    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024 / 1024
        reserved = torch.cuda.memory_reserved() / 1024 / 1024
        logger.debug(f"显存分配: {allocated:.2f} MB，显存保留: {reserved:.2f} MB")
