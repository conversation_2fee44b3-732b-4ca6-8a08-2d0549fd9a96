import json

class RTSPConfig:
    def __init__(self, rtsp_url, reconnect_wait_time, error_wait_time):
        self.rtsp_url = rtsp_url
        self.reconnect_wait_time = reconnect_wait_time
        self.error_wait_time = error_wait_time

def get_rtsp_config(config_path="config.json") -> RTSPConfig:
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)
    rtsp = config.get("rtsp", {})
    return RTSPConfig(
        rtsp_url=rtsp.get("url", ""),
        reconnect_wait_time=rtsp.get("reconnect_wait_time", 2),
        error_wait_time=rtsp.get("error_wait_time", 0.5)
    )
