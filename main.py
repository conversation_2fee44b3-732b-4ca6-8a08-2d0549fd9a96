import time
import numpy as np
import json
import torch
import traceback
import gc  # 用于垃圾回收
from ultralytics import YOLO
from log_manager import setup_logger
from rtsp_stream.rtsp_streamer import RTSPStreamer
from rtsp_stream.config import get_rtsp_config

torch.set_num_threads(1)  # 避免多线程崩溃

# 加载配置
with open("config.json", "r", encoding="utf-8") as f:
    config = json.load(f)

model_path = config["model"]["model_path"]
device = config["model"].get("device", "cuda" if torch.cuda.is_available() else "cpu")
log_config = config["log"]
frame_interval = config.get("frame_interval", 1)

logger = setup_logger(log_config)

# 统计帧数用于定期打印系统状态
frame_counter = 0
print_every_n_frames = 30


def calculate_area_percent(mask: np.ndarray) -> float:
    total_pixels = mask.shape[0] * mask.shape[1]
    detected_pixels = np.sum(mask > 0)
    return (detected_pixels / total_pixels) * 100


def classify_level(pct: float) -> str:
    if pct < 20:
        return "Level1 - 空载/极轻载"
    elif 20 <= pct < 45:
        return "Level2 - 轻载"
    elif 45 <= pct < 70:
        return "Level3 - 中载"
    else:
        return "Level4 - 重载"


def main():
    logger.info("湿渣检测系统启动（RTSP + YOLOv8 分割）")
    model = YOLO(model_path)
    logger.info(f"模型加载成功: {model_path}")

    rtsp_cfg = get_rtsp_config()
    streamer = RTSPStreamer(
        rtsp_url=rtsp_cfg.rtsp_url,
        reconnect_wait_time=rtsp_cfg.reconnect_wait_time,
        error_wait_time=rtsp_cfg.error_wait_time
    )

    if not streamer.connect():
        logger.error("RTSP 连接失败")
        return

    global frame_counter

    try:
        while True:
            frame = streamer.read_frame()
            if frame is None:
                logger.warning("无法读取视频帧，尝试重连...")
                time.sleep(rtsp_cfg.error_wait_time)
                continue

            try:
                results = model(frame, device=device, verbose=False, task='segment')
                masks = results[0].masks

                if masks is None or masks.data is None:
                    logger.info("当前帧未检测到渣区域")
                    time.sleep(frame_interval)
                    continue

                masks_tensor = masks.data
                if not hasattr(masks_tensor, "shape") or masks_tensor.shape[0] == 0:
                    logger.info("掩码数据为空或无效")
                    time.sleep(frame_interval)
                    continue

                masks_np = masks_tensor.cpu().numpy()
                logger.debug(f"masks shape: {masks_np.shape}")

                combined_mask = np.any(masks_np, axis=0).astype(np.uint8)
                area_pct = calculate_area_percent(combined_mask)
                level = classify_level(area_pct)
                msg = f"渣量占比：{area_pct:.2f}%，等级：{level}"
                logger.info(msg)

                # ✅ 显存 + 内存清理
                del results
                del masks_tensor
                del masks_np
                del combined_mask
                torch.cuda.empty_cache()
                gc.collect()

                # ✅ 每 N 帧计数器递增（无系统状态打印）
                frame_counter += 1

            except Exception as e:
                logger.error(f"推理或处理异常: {e}")
                logger.debug(traceback.format_exc())
                time.sleep(frame_interval)
                continue

            time.sleep(frame_interval)

    except KeyboardInterrupt:
        logger.info("手动终止检测")
    finally:
        streamer.release()


if __name__ == "__main__":
    main()
