import numpy as np


def calculate_area_percent(mask: np.ndarray) -> float:
    """计算掩膜区域在图像中所占比例（0~100%）"""
    total_pixels = mask.shape[0] * mask.shape[1]
    detected_pixels = np.sum(mask > 0)
    return (detected_pixels / total_pixels) * 100


def classify_level(pct: float) -> str:
    """根据渣量占比进行等级划分"""
    if pct < 20:
        return "Level1 - 空载/极轻载"
    elif 20 <= pct < 45:
        return "Level2 - 轻载"
    elif 45 <= pct < 70:
        return "Level3 - 中载"
    else:
        return "Level4 - 重载"
