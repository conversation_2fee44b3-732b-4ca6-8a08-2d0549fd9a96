## 湿渣识别检测系统
基于 YOLOv8 分割模型的传送带湿渣实时检测系统，支持通过 RTSP 视频流持续监测渣量等级与刮板状态，实现智能识别、报警上报、日志记录与系统监控。

## 系统特性
+ **实时视频分析**：通过 RTSP 流持续监控湿渣状态
+ **分割识别模型**：采用 YOLOv8-seg 模型分割渣堆区域
+ **多帧融合**：集成多帧推理结果提升检测精度
+ **滑动窗口平滑**：降低单帧波动对整体判断的影响
+ **模型热更新**：可实时加载更新后的模型文件
+ **内存显存监控**：记录运行状态，辅助系统维护与调试

## 渣量等级划分
| 等级 | 渣量占比范围 | 描述 | 指示色 |
| --- | --- | --- | --- |
| Level1 | 0–20% | 空载 / 极轻载 | 黑色 |
| Level2 | 21–45% | 轻载 | 蓝色 |
| Level3 | 46–70% | 中载 | 橙色 |
| Level4 | 71–100% | 满载 / 堵料 | 红色 |


## 项目结构
```plain
wet_slag_detect/
├── main.py                  # 主程序入口
├── utils.py                 # 工具函数（掩膜面积计算、等级判定等）
├── monitor.py               # 显存、内存监控模块
├── log_manager.py           # 日志记录模块
├── requirements.txt         # 依赖项
├── config.json              # 配置文件
├── rtsp_stream/             # RTSP流处理模块
│   ├── __init__.py
│   ├── rtsp_streamer.py
│   ├── config.py
│   └── logger.py
├── models/                  # 模型文件夹
│   └── best.pt              # YOLOv8-seg模型权重
├── logs/                    # 日志文件目录
```

## 环境要求
+ Python 3.8+
+ Ultralytics YOLOv8
+ OpenCV
+ CUDA支持（可选，用于GPU加速）

## <font style="color:rgb(59, 59, 59);">安装与运行</font>
### <font style="color:rgb(59, 59, 59);">1. 安装依赖</font>
```bash
pip install -r requirements.txt
```

### <font style="color:rgb(59, 59, 59);">2. 模型准备</font>
<font style="color:rgb(59, 59, 59);">将你训练好的 YOLOv8 分割模型放入</font><font style="color:rgb(59, 59, 59);"> </font>`models/`<font style="color:rgb(59, 59, 59);"> </font><font style="color:rgb(59, 59, 59);">目录：</font>

```plain
models/
└── best.pt
```

### <font style="color:rgb(59, 59, 59);">3. 配置参数</font>
<font style="color:rgb(59, 59, 59);">编辑</font><font style="color:rgb(59, 59, 59);"> </font>`config.json`<font style="color:rgb(59, 59, 59);"> </font><font style="color:rgb(59, 59, 59);">文件：</font>

```json
{
  "model": {
    "model_path": "models/best.pt",
    "device": "cuda",
    "image_size": 640
  },
  "rtsp": {
    "url": "rtsp://localhost:8554/level",
    "reconnect_wait_time": 2,
    "error_wait_time": 0.5
  },
  "detection": {
    "interval_seconds": 5,
    "frames_per_detection": 3,
    "smoothing_window": 5
  },
  "log": {
    "save_logs": true,
    "output_dir": "logs",
    "clean_logs": true,
    "clean_day": 7
  }
}
```

### <font style="color:rgb(59, 59, 59);">4. 启动系统</font>
```bash
python main.py
```

## <font style="color:rgb(59, 59, 59);">输出示例</font>
### <font style="color:rgb(59, 59, 59);">控制台日志输出</font>
```plain
2025-08-05 10:15:02 - 湿渣检测系统启动
2025-08-05 10:15:03 - 成功加载模型: models/best.pt
2025-08-05 10:15:04 - RTSP连接成功
2025-08-05 10:15:10 - 检测结果: 渣量占比16.2%，等级Level1 - 空载
```



## <font style="color:rgb(59, 59, 59);">系统监控与维护</font>
+ **<font style="color:rgb(59, 59, 59);">日志</font>**<font style="color:rgb(59, 59, 59);">：每日生成日志，保存在</font><font style="color:rgb(59, 59, 59);"> </font>`logs/`<font style="color:rgb(59, 59, 59);"> </font><font style="color:rgb(59, 59, 59);">文件夹，可自动清理旧文件</font>
+ **<font style="color:rgb(59, 59, 59);">状态监控</font>**<font style="color:rgb(59, 59, 59);">：记录内存、显存占用，便于系统调优</font>
+ **<font style="color:rgb(59, 59, 59);">模型热更新</font>**<font style="color:rgb(59, 59, 59);">：无需重启即可加载新模型（监听路径变更）</font>

